# Repository Configuration System

This document describes the Django-like repository configuration system that allows you to choose between different repository implementations for workflow execution and node execution.

## Overview

The repository configuration system provides a flexible way to configure different repository implementations without changing code. This is similar to Django's database backend configuration where you specify the backend through settings.

## Configuration

Repository implementations are configured through environment variables or the `.env` file:

### Environment Variables

```bash
# WorkflowExecution repository implementation
WORKFLOW_EXECUTION_REPOSITORY=core.repositories.sqlalchemy_workflow_execution_repository.SQLAlchemyWorkflowExecutionRepository

# WorkflowNodeExecution repository implementation  
WORKFLOW_NODE_EXECUTION_REPOSITORY=core.repositories.sqlalchemy_workflow_node_execution_repository.SQLAlchemyWorkflowNodeExecutionRepository
```

### Configuration Format

Repository implementations are specified as Python module paths in the format:
```
module.submodule.ClassName
```

For example:
- `core.repositories.sqlalchemy_workflow_execution_repository.SQLAlchemyWorkflowExecutionRepository`
- `my_custom_package.repositories.redis_workflow_execution_repository.RedisWorkflowExecutionRepository`

## Default Implementations

The system comes with default SQLAlchemy-based implementations:

- **WorkflowExecutionRepository**: `SQLAlchemyWorkflowExecutionRepository`
- **WorkflowNodeExecutionRepository**: `SQLAlchemyWorkflowNodeExecutionRepository`

These implementations provide:
- Multi-tenancy support
- In-memory caching
- Transaction management
- Database persistence using SQLAlchemy

## Creating Custom Implementations

To create a custom repository implementation:

### 1. Implement the Repository Interface

Your custom repository must implement the appropriate protocol interface:

```python
from core.workflow.repositories.workflow_execution_repository import WorkflowExecutionRepository
from core.workflow.entities.workflow_execution import WorkflowExecution
from typing import Optional, Union
from sqlalchemy.orm import sessionmaker
from sqlalchemy.engine import Engine
from models import Account, EndUser
from models.enums import WorkflowRunTriggeredFrom

class MyCustomWorkflowExecutionRepository(WorkflowExecutionRepository):
    def __init__(
        self,
        session_factory: Union[sessionmaker, Engine],
        user: Union[Account, EndUser],
        app_id: str,
        triggered_from: WorkflowRunTriggeredFrom,
    ):
        # Initialize your repository
        pass
    
    def save(self, execution: WorkflowExecution) -> None:
        # Implement save logic
        pass
    
    def get(self, execution_id: str) -> Optional[WorkflowExecution]:
        # Implement get logic
        pass
```

### 2. Required Constructor Parameters

All repository implementations must accept these constructor parameters:

- `session_factory`: SQLAlchemy sessionmaker or Engine
- `user`: Account or EndUser object (for multi-tenancy)
- `app_id`: Application ID string
- `triggered_from`: Trigger source enum

### 3. Required Methods

#### WorkflowExecutionRepository
- `save(execution: WorkflowExecution) -> None`
- `get(execution_id: str) -> Optional[WorkflowExecution]`

#### WorkflowNodeExecutionRepository
- `save(execution: WorkflowNodeExecution) -> None`
- `get_by_node_execution_id(node_execution_id: str) -> Optional[WorkflowNodeExecution]`
- `get_by_workflow_execution_id(workflow_execution_id: str, order_config: Optional[OrderConfig] = None) -> Sequence[WorkflowNodeExecution]`
- `delete_by_workflow_execution_id(workflow_execution_id: str) -> None`

## Example: Redis Repository Implementation

Here's an example of how you might implement a Redis-based repository:

```python
# my_package/repositories/redis_workflow_execution_repository.py
import json
import redis
from typing import Optional, Union
from core.workflow.repositories.workflow_execution_repository import WorkflowExecutionRepository
from core.workflow.entities.workflow_execution import WorkflowExecution

class RedisWorkflowExecutionRepository(WorkflowExecutionRepository):
    def __init__(self, session_factory, user, app_id, triggered_from):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.user = user
        self.app_id = app_id
        self.triggered_from = triggered_from
    
    def save(self, execution: WorkflowExecution) -> None:
        key = f"workflow_execution:{execution.id}"
        data = execution.model_dump_json()
        self.redis_client.set(key, data)
    
    def get(self, execution_id: str) -> Optional[WorkflowExecution]:
        key = f"workflow_execution:{execution_id}"
        data = self.redis_client.get(key)
        if data:
            return WorkflowExecution.model_validate_json(data)
        return None
```

Then configure it:
```bash
WORKFLOW_EXECUTION_REPOSITORY=my_package.repositories.redis_workflow_execution_repository.RedisWorkflowExecutionRepository
```

## Validation

The system includes validation to ensure configured repositories are valid:

### Startup Validation

You can validate all repository configurations at startup:

```python
from core.repositories import RepositoryFactory

try:
    RepositoryFactory.validate_configuration()
    print("All repository configurations are valid")
except RepositoryImportError as e:
    print(f"Repository configuration error: {e}")
```

### Runtime Validation

The factory automatically validates repositories when creating instances:
- Checks that the class can be imported
- Validates that required methods are implemented
- Verifies constructor signature matches expected parameters

## Error Handling

The system provides detailed error messages for common configuration issues:

- **Import errors**: When the specified module or class cannot be found
- **Interface errors**: When the class doesn't implement required methods
- **Constructor errors**: When the constructor doesn't accept required parameters
- **Runtime errors**: When repository instantiation fails

## Best Practices

1. **Validate Early**: Call `RepositoryFactory.validate_configuration()` at application startup
2. **Use Type Hints**: Implement proper type hints in your custom repositories
3. **Handle Errors**: Implement proper error handling in your repository methods
4. **Test Thoroughly**: Test your custom repositories with the same test cases as the default implementations
5. **Document Dependencies**: Clearly document any external dependencies your custom repository requires

## Migration Guide

To migrate from hardcoded repository instantiation to the configuration system:

### Before
```python
from core.repositories.sqlalchemy_workflow_execution_repository import SQLAlchemyWorkflowExecutionRepository

repository = SQLAlchemyWorkflowExecutionRepository(
    session_factory=session_factory,
    user=user,
    app_id=app_id,
    triggered_from=triggered_from,
)
```

### After
```python
from core.repositories import RepositoryFactory

repository = RepositoryFactory.create_workflow_execution_repository(
    session_factory=session_factory,
    user=user,
    app_id=app_id,
    triggered_from=triggered_from,
)
```

The factory will automatically use the configured implementation based on your environment variables.

## Testing Your Configuration

You can test your repository configuration with this simple script:

```python
from core.repositories import RepositoryFactory

# Validate configuration at startup
try:
    RepositoryFactory.validate_configuration()
    print("✅ Repository configuration is valid")
except Exception as e:
    print(f"❌ Configuration error: {e}")

# Test importing configured classes
try:
    workflow_class = RepositoryFactory._import_class(
        "core.repositories.sqlalchemy_workflow_execution_repository.SQLAlchemyWorkflowExecutionRepository"
    )
    print(f"✅ Can import: {workflow_class.__name__}")
except Exception as e:
    print(f"❌ Import error: {e}")
```

## Environment Variable Examples

### Using Default SQLAlchemy Implementation
```bash
# .env file
WORKFLOW_EXECUTION_REPOSITORY=core.repositories.sqlalchemy_workflow_execution_repository.SQLAlchemyWorkflowExecutionRepository
WORKFLOW_NODE_EXECUTION_REPOSITORY=core.repositories.sqlalchemy_workflow_node_execution_repository.SQLAlchemyWorkflowNodeExecutionRepository
```

### Using Custom Implementation
```bash
# .env file
WORKFLOW_EXECUTION_REPOSITORY=my_company.repositories.custom_workflow_execution_repository.CustomWorkflowExecutionRepository
WORKFLOW_NODE_EXECUTION_REPOSITORY=my_company.repositories.custom_workflow_node_execution_repository.CustomWorkflowNodeExecutionRepository
```

### Using Third-Party Implementation
```bash
# .env file
WORKFLOW_EXECUTION_REPOSITORY=third_party_package.repositories.redis_workflow_execution_repository.RedisWorkflowExecutionRepository
WORKFLOW_NODE_EXECUTION_REPOSITORY=third_party_package.repositories.redis_workflow_node_execution_repository.RedisWorkflowNodeExecutionRepository
```
